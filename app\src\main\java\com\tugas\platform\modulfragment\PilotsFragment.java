package com.tugas.platform.modulfragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

public class PilotsFragment extends Fragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_pilots, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        TextView titleText = view.findViewById(R.id.tv_title);
        TextView contentText = view.findViewById(R.id.tv_content);
        
        titleText.setText("Pilots");
        
        String pilotsContent = "GUNDAM MEISTERS\n\n" +
                "1. Setsuna F. Seiei\n" +
                "   - Gundam: Exia → 00 Gundam\n" +
                "   - Asal: Republik Krugis\n" +
                "   - Kepribadian: Pendiam, fokus pada misi\n" +
                "   - Motto: \"Aku adalah Gundam\"\n\n" +
                
                "2. Lockon Stratos (Neil Dylandy)\n" +
                "   - Gundam: Dynames\n" +
                "   - Asal: Irlandia\n" +
                "   - Kepribadian: Ramah, pelindung tim\n" +
                "   - Keahlian: Penembak jitu terbaik\n\n" +
                
                "3. Allelujah Haptism\n" +
                "   - Gundam: Kyrios → Arios\n" +
                "   - Asal: Human Reform League\n" +
                "   - Kepribadian: Lembut, berkepribadian ganda\n" +
                "   - Keunikan: Memiliki alter ego Hallelujah\n\n" +
                
                "4. Tieria Erde\n" +
                "   - Gundam: Virtue → Seravee\n" +
                "   - Asal: Innovator buatan\n" +
                "   - Kepribadian: Dingin, logis, setia pada misi\n" +
                "   - Keunikan: Terhubung dengan Veda\n\n" +
                
                "KARAKTER PENDUKUNG:\n" +
                "• Sumeragi Lee Noriega - Taktisi Ptolemaios\n" +
                "• Feldt Grace - Operator komunikasi\n" +
                "• Christina Sierra - Navigator\n" +
                "• Lasse Aeon - Helmsman";
        
        contentText.setText(pilotsContent);
    }
}
