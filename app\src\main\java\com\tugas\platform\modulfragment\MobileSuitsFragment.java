package com.tugas.platform.modulfragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

public class MobileSuitsFragment extends Fragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_mobile_suits, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        TextView titleText = view.findViewById(R.id.tv_title);
        TextView contentText = view.findViewById(R.id.tv_content);
        
        titleText.setText("Mobile Suits");
        
        String mobileSuitsContent = "GUNDAM 00 MOBILE SUITS\n\n" +
                "1. GN-001 Gundam Exia\n" +
                "   - Pilot: Setsuna F. Seiei\n" +
                "   - Spesialisasi: Pertempuran jarak dekat\n" +
                "   - Senjata: GN Sword, GN Blade\n\n" +
                
                "2. GN-002 Gundam Dynames\n" +
                "   - Pilot: Lockon Stratos\n" +
                "   - Spesialisasi: Penembak jitu\n" +
                "   - Senjata: GN Sniper Rifle\n\n" +
                
                "3. GN-003 Gundam Kyrios\n" +
                "   - Pilot: Allelujah Haptism\n" +
                "   - Spesialisasi: Transformasi dan mobilitas\n" +
                "   - Senjata: GN Submachine Gun\n\n" +
                
                "4. GN-005 Gundam Virtue\n" +
                "   - Pilot: Tieria Erde\n" +
                "   - Spesialisasi: Artileri berat\n" +
                "   - Senjata: GN Bazooka, GN Cannon\n\n" +
                
                "5. GN-0000 00 Gundam\n" +
                "   - Pilot: Setsuna F. Seiei\n" +
                "   - Spesialisasi: Twin Drive System\n" +
                "   - Senjata: GN Sword II, GN Sword III";
        
        contentText.setText(mobileSuitsContent);
    }
}
