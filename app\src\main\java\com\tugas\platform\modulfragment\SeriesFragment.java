package com.tugas.platform.modulfragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

public class SeriesFragment extends Fragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_series, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        TextView titleText = view.findViewById(R.id.tv_title);
        TextView contentText = view.findViewById(R.id.tv_content);
        
        titleText.setText("Series / Timeline");
        
        String seriesContent = "MOBILE SUIT GUNDAM 00\n\n" +
                "INFORMASI SERIES:\n" +
                "• Tahun Rilis: 2007-2009\n" +
                "• Studio: Sunrise\n" +
                "• Sutradara: Seiji Mizushima\n" +
                "• Total Episode: 50 episode (2 season)\n\n" +
                
                "TIMELINE CERITA:\n\n" +
                "SEASON 1 (2307 AD):\n" +
                "• Celestial Being memulai intervensi bersenjata\n" +
                "• Perkenalan 4 Gundam Meister\n" +
                "• Konflik dengan 3 blok kekuatan dunia\n" +
                "• Pembentukan UN Forces\n" +
                "• Kehancuran Ptolemaios\n\n" +
                
                "SEASON 2 (2312 AD):\n" +
                "• Munculnya A-Laws dan Innovators\n" +
                "• Celestial Being bangkit kembali\n" +
                "• Gundam generasi ke-4 diperkenalkan\n" +
                "• Pertempuran melawan Ribbons Almark\n" +
                "• Setsuna menjadi True Innovator\n\n" +
                
                "MOVIE: A Wakening of the Trailblazer (2314 AD):\n" +
                "• Invasi alien ELS\n" +
                "• Setsuna sebagai jembatan komunikasi\n" +
                "• 00 Qan[T] debut\n" +
                "• Perdamaian dengan ELS\n\n" +
                
                "TEMA UTAMA:\n" +
                "• Penghapusan perang melalui kekuatan\n" +
                "• Evolusi manusia (Innovator)\n" +
                "• Pemahaman dan komunikasi antar spesies";
        
        contentText.setText(seriesContent);
    }
}
